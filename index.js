const express = require('express');
const path = require('path');
const fs = require('fs');
const cors = require("cors");
const { connectDB } = require('./configs/dbCon');  
require('dotenv').config();  
const app = express();
connectDB();
const PORT = 3001;
// ======= Body Parser =======
app.use(express.json());  
app.use(express.urlencoded({ extended: true })); 

// ======= Static Files =======
const uploadsDir = path.join(__dirname, 'uploads');

// make sure folder exists first
if (!fs.existsSync(uploadsDir)) {
    fs.mkdirSync(uploadsDir, { recursive: true });
}

// then serve it
app.use('/uploads', express.static(uploadsDir));




//app.set('trust proxy', true);
app.use(cors({
  origin: [
    "https://adityaastro.in",    // your frontend domain
    "http://localhost:3000"      // for local testing
  ],
  methods: ["GET", "POST", "PUT", "DELETE"],
  allowedHeaders: ["Content-Type", "Authorization"],
  credentials: true
}));



// ======= Api Routes =======
const apiRoutes = require('./routes/apiRoutes');
app.use('/api', apiRoutes);

// ======= Server =======
app.listen(PORT, '0.0.0.0', () => {
    console.log(`Api is Running on 🚀 => http://localhost:${PORT}`);
});








