const jwt = require("jsonwebtoken");
const Session = require("../models/sessionModel");
const User = require("../models/usersModel");

const getClientIp = (req) => {
  const ipWithPort = req.ip || (req.socket && req.socket.remoteAddress) || '';
  return ipWithPort.includes(':') ? ipWithPort.split(':').pop() : ipWithPort;
};

/**
 * @param {...string} allowedRoles - Optional roles to check
 */
const authMiddleware = (...allowedRoles) => {
  return async (req, res, next) => {
    try {
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith("Bearer ")) {
        return res.status(401).json({ message: "Token missing" });
      }
      const authToken = authHeader.split(" ")[1];
      req.authToken = authToken;
      const decoded = jwt.verify(authToken, process.env.JWT_SECRET);
      const currentIp = getClientIp(req);

      const session = await Session.findOne({
        where: {
          userId: decoded.id,
          authToken,
          isLoggedIn: true,
          ipAddress: currentIp,
        },
      });

      if (!session) {
        await Session.update(
          { isLoggedIn: false, logoutTime: new Date() },
          { where: { userId: decoded.id, isLoggedIn: true } }
        );
        return res.status(401).json({ message: "Session invalidated. Please login again." });
      }

      if (new Date() > session.expiresAt) {
        await Session.update(
          { isLoggedIn: false, logoutTime: new Date() },
          { where: { id: session.id } }
        );
        return res.status(401).json({ message: "Session expired. Please login again." });
      }

      const user = await User.findByPk(decoded.id);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }

      req.user = {
        id: user.id,
        fullName: user.firstName + " " + user.lastName,
        username: user.username,
        email: user.email,
        mobile: user.mobile,
        role: user.role,
        isVerified: user.isVerified,
        status: user.status,
      };

      if (allowedRoles.length > 0 && !allowedRoles.includes(user.role)) {
        return res.status(403).json({ message: "Access denied" });
      }

      next();
    } catch (error) {
      return res.status(401).json({ message: "Unauthorized", error: error.message });
    }
  };
};

module.exports = { authMiddleware };
