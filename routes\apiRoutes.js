const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { authMiddleware } = require('../middlewares/authMiddleware');
const { fileUpload } = require('../middlewares/uploadMiddleware');
const { 
  registerUser, login, logout, getAllUsersList, getInduvisualUserById, changePassword, forgotPassword 
} = require('../controllers/usersController');

const { 
  createProduct, getAllProducts, getProductById, createCategory, getCategories, getProductByCategory ,updateProduct
} = require('../controllers/productsController');

const { CreateBlog, GetBlogs, UpdateBlog, DeleteBlog } = require('../controllers/blogController');

// ===== Multer configuration =====
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const tempPath = path.join(__dirname, 'uploads', 'temp');
    fs.mkdirSync(tempPath, { recursive: true });
    cb(null, tempPath);
  },
  filename: function (req, file, cb) {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const uploadSingle = multer({ storage }).single('file');        // for blog featured image
const uploadMultiple = multer({ storage }).array('images', 5);  // for product gallery images

// ===== Base API Route =====
router.get('/', (req, res) => res.send('API Is Working!'));

// ===== User Routes =====
router.post('/register', registerUser);
router.post('/login', login);
router.post('/logout', authMiddleware("admin","user"), logout);
router.get("/get-single-user/:id", authMiddleware("admin","user"), getInduvisualUserById);
router.get("/users-list", authMiddleware("admin"), getAllUsersList);
router.post("/change-password", authMiddleware("admin","user"), changePassword);
router.post("/forgot-password", forgotPassword);

// ===== Product Routes =====
router.post("/add-product", authMiddleware("admin"), uploadMultiple, createProduct);
router.get("/get-all-products", getAllProducts);
router.get("/get-product-by-id/:id", getProductById);
router.get("/get-product-by-category/:categoryMasterId", getProductByCategory);
router.post("/update-product/:id", authMiddleware("admin"), uploadMultiple, updateProduct);

// ===== Category Routes =====
router.post("/add-category", authMiddleware("admin"), createCategory);
router.get("/categories", getCategories);

// ===== Blog Routes =====
router.get("/get-blogs", GetBlogs);
router.post("/create-blog",authMiddleware("admin"),fileUpload({folder: 'uploads/blogs',field: 'file',required: true,
    maxCount: 1,
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/jpg'],
    maxSizeKB: 1024,
    customName: () => `BlogImg-${Date.now()}`
  }),
  CreateBlog
);

router.post("/update-blog/:id", authMiddleware("admin"), uploadSingle, UpdateBlog);
router.delete("/delete-blog/:id", authMiddleware("admin"), DeleteBlog);

// ===== Orders Routes =====
// TODO: Implement order controller functions
// router.post("/add-order", authMiddleware("admin","user"), createOrder);
// router.get("/get-all-orders", authMiddleware("admin"), getAllOrders);
// router.get("/get-order-by-id/:id", authMiddleware("admin","user"), getOrderById);

// ===== Export router =====
module.exports = router;
