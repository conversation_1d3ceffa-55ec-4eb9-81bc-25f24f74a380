const { DataTypes } = require("sequelize");
const { Sequelizer} = require("../configs/dbCon");
const Session = Sequelizer.define("Session",
    {
        id: {
            type: DataTypes.UUID,
            defaultValue: DataTypes.UUIDV4, 
            allowNull: false,
            primaryKey: true,
        },
        userId: {
            type: DataTypes.UUID,
            allowNull: false,
            unique: true,
        },
        authToken: {
            type: DataTypes.TEXT, // Use TEXT for long text
            allowNull: false,
            comment: "JWT token for authentication",
        },
        ipAddress: {
            type: DataTypes.STRING,
            allowNull: true,
            comment: "IP address from which the user logged in",
        },
        loginTime: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW,
            comment: "When the user logged in",
        },
        logoutTime: {
            type: DataTypes.DATE,
            allowNull: true,
            comment: "When the user logged out",
        },
        expiresAt: {
            type: DataTypes.DATE,
            allowNull: false,
            comment: "Token/session expiry time",
        },
        isLoggedIn: {
            type: DataTypes.BOOLEAN,
            defaultValue: true,
            comment: "Whether the session is active",
        },
    },
    {
        tableName: "sessions",
        timestamps: true,
        hooks: {
            beforeCreate: (session) => {
                // Auto set expiry if not provided (default: 7 days from now)
                if (!session.expiresAt) {
                    session.expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
                }
            },
            beforeSave: (session) => {
                // Check if expired before saving
                if (session.expiresAt && new Date() > session.expiresAt) {
                    session.isLoggedIn = false;
                    session.logoutTime = new Date();
                }
            },
        },
    }
);

module.exports = Session;
