const { DataTypes } = require("sequelize");
const { Sequelizer } = require("../configs/dbCon");

const Blog = Sequelizer.define(
  "Blog",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },

    title: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },

    category: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },

    tags: {
      type: DataTypes.JSON, // store tags as array ["tech","news"]
      allowNull: true,
    },

    content: {
      type: DataTypes.TEXT, // main body
      allowNull: false,
    },

    excerpt: {
      type: DataTypes.STRING(500), // short summary
      allowNull: true,
    },

    featuredImage: {
      type: DataTypes.STRING, // URL of image
      allowNull: true,
    },

    status: {
      type: DataTypes.ENUM("draft", "published", "archived"),
      defaultValue: "draft",
    },

    publishDate: {
      type: DataTypes.DATE,
      allowNull: true,
    },

    author: {
      type: DataTypes.STRING(150),
      allowNull: false,
    },

    metaTitle: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },

    metaDescription: {
      type: DataTypes.STRING(500),
      allowNull: true,
    },
  },
  {
    tableName: "blogs",
    timestamps: true,
    paranoid: true, // soft delete
  }
);

module.exports = Blog;
