const { Sequelize } = require("sequelize");
require("dotenv").config();
const Sequelizer = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    dialect: process.env.DB_DIALECT,
    port: process.env.DB_PORT || 3306,
    logging: false,
    pool: { 
      max: 10, 
      min: 0, 
      acquire: 30000, 
      idle: 10000 
    },
   indexes: [
  { unique: true, fields: ["email"] }
]
  }
);


const connectDB = async () => {
  try {
    await Sequelizer.authenticate();
    console.log("✅ DB Connected To MariaDB/MySQL Via Mysql2");
    console.log("type:", process.env.SERVER_TYPE);
    switch (process.env.SERVER_TYPE) {
      case "PRODUCTION":
        await Sequelizer.sync(); // only create missing tables
        console.log("✅ PRODUCTION DB Synced");
        break;

      case "STAGING":
        //await Sequelizer.sync({ alter: true,force:true }); // alter tables to match models
        console.log("✅ STAGING DB Synced (alter enabled)");
        break;

      case "DEV":
      case "DEVELOPMENT":
        await Sequelizer.sync({ alter: true });
        console.log("✅ DEVELOPMENT DB Synced (with alter)");
        break;

      default:
        console.log("⚠️ No SERVER_TYPE matched. Skipping Sequelize sync...");
    }
  } catch (error) {
    console.error("❌ DB Connection Failed:", error.message);
    process.exit(1);
  }
};

module.exports = { connectDB, Sequelizer };
