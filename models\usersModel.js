const { DataTypes } = require("sequelize");
const { Sequelizer } = require("../configs/dbCon");
const User = Sequelizer.define("User",
    {
        id: {
            type: DataTypes.UUID,
            defaultValue: DataTypes.UUIDV4,
            allowNull: false,
            primaryKey: true,
        },
        firstName: {
            type: DataTypes.STRING(100),
            allowNull: false,
        },
        lastName: {
            type: DataTypes.STRING(100),
            allowNull: true,
        },
        username: {
            type: DataTypes.STRING(50),
            allowNull: false,
            unique: true,
        },
        email: {
            type: DataTypes.STRING(150),
            allowNull: false,
            unique: true,
            validate: {
                isEmail: { msg: "Invalid email format" },
            },
        },
        mobile: {
            type: DataTypes.STRING(15),
            allowNull: false,
            unique: true,
        },

        password: {
            type: DataTypes.STRING,
            allowNull: false,
        },

        role: {
            type: DataTypes.ENUM("user", "admin"),
            defaultValue: "user",
        },
        isVerified: {
            type: DataTypes.BOOLEAN,
            defaultValue: false,
        },
        status: {
            type: DataTypes.ENUM("active", "inactive", "blocked"),
            defaultValue: "active",
        },

        profileImage: {
            type: DataTypes.STRING,
            allowNull: true,
        },
        shippingAddress: {
            type: DataTypes.JSON,
            allowNull: true,
        },
        billingAddress: {
            type: DataTypes.JSON,
            allowNull: true,
        },
        wishlist: {
            type: DataTypes.JSON,
            allowNull: true,
        },

        cart: {
            type: DataTypes.JSON,
            allowNull: true,
        },
    },
    {
        tableName: "users",
        timestamps: true,
        paranoid: true,
    }
);

module.exports = User;
