{"name": "astrobakend", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "HOST=0.0.0.0 PORT=4000 nodemon index.js", "start": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "<PERSON><PERSON> , <PERSON><PERSON>", "license": "ISC", "description": "Astrology Project Backend", "dependencies": {"@types/ol": "^6.5.3", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-async-handler": "^1.2.0", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.2", "mysql2": "^3.14.3", "nodemon": "^3.1.10", "ol": "^10.6.1", "sequelize": "^6.37.7", "uuid": "^11.1.0", "xss-clean": "^0.1.4"}}