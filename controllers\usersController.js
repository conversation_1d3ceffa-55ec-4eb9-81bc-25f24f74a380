const bcrypt = require("bcryptjs");
const jwt = require("jsonwebtoken");
const asyncHandler = require("express-async-handler");
const { generateToken } = require("../configs/genToken");
const User = require("../models/usersModel");
const Session = require("../models/sessionModel");
const { Op } = require('sequelize');


// REGISTER
const registerUser = asyncHandler(async (req, res) => {
  try {
    const { firstName, lastName, username, email, mobile, password } = req.body;

    const existingEmail = await User.findOne({ where: { email } });
    if (existingEmail) {
      return res.status(409).json({ message: "Email is already registered" });
    }
    const existingMobile = await User.findOne({ where: { mobile } });
    if (existingMobile) {
      return res.status(409).json({ message: "Mobile is already registered" });
    }
    const existingUsername = await User.findOne({ where: { username } });
    if (existingUsername) {
      return res.status(409).json({ message: "Username is already taken" });
    }
    const hashedPassword = await bcrypt.hash(password, 16);
    const user = await User.create({
      firstName,
      lastName,
      username,
      email,
      mobile,
      password: hashedPassword,
    });
    const userSafe = {
      fullName: user.firstName + " " + user.lastName,
      username: user.username,
      mobile: user.mobile,
      email: user.email,
    };
    res.status(201).json({ message: "User registered successfully", userInfo: userSafe });
  } catch (error) {
    res.status(500).json({ message: "Registration failed", error: error.message });
  }
});


// LOGIN
const login = asyncHandler(async (req, res) => {
  console.log("req",req.body);
  try {
    const { identifier, password } = req.body;
    const ipWithPort = req.ip || (req.socket && req.socket.remoteAddress) || '';
    const ip = ipWithPort.split(':').pop();
    const user = await User.findOne({
      where: {
        [Op.or]: [
          { email: identifier },
          { mobile: identifier },
          { username: identifier }
        ]
      }
    });

    if (!user) {
      return res.status(401).json({ message: "Invalid Credentials" });
    }
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({ message: "Invalid Credentials" });
    }
console.log("i am here");
const userInfo = {
    id: user.id,
    fullName: user.firstName+" "+user.lastName,
    username: user.username,
    email: user.email,
    mobile: user.mobile,
    role: user.role,
    isVerified: user.isVerified,
    status: user.status,
  };

    const authToken = generateToken({ ...userInfo });

    //await Session.destroy({ where: { userId: user.id, isLoggedIn: true } });

    // Create new session
  //  await Session.create({
  //     userId: user.id,
  //     authToken,
  //     ipAddress: ip,
  //     loginTime: new Date(),
  //     expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
  //     isLoggedIn: true,
  //   });

    res.status(200).json({ message: "Login Successfully", authToken });
  } catch (error) {
    console.log("Login error:", error);
    res.status(500).json({ message: "Login failed", error: error.message });
  }
});

// LOGOUT
const logout = asyncHandler(async (req, res) => {
  try {
         const authToken = req.headers.authorization?.split(" ")[1] || req.query.authToken || req.body.authToken ||req.authToken;

    if (!authToken) {
      return res.status(401).json({ message: "No Token Provided" });
    }

    const decoded = jwt.verify(authToken, process.env.JWT_SECRET);

    await Session.update(
      { isLoggedIn: false, logoutTime: new Date() },
      { where: { userId: decoded.id, authToken, isLoggedIn: true } }
    );

    res.status(200).json({ message: "Logged Out Successfully" });
  } catch (error) {
    res.status(500).json({ message: "Logout failed", error: error.message });
  }
});





// getAllUsersList
const getAllUsersList = asyncHandler(async (req, res) => {
  try {
    const users = await User.findAll({
      attributes: { exclude: ["password"] },
    });

    res.status(200).json({ count: users.length, data: users });
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch users", error: error.message });
  }
});

// getInduvisualUserById
const getInduvisualUserById = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;
    const user = await User.findByPk(id, {
      attributes: { exclude: ["password"] },
    });

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    res.status(200).json(user);
  } catch (error) {
    res.status(500).json({ message: "Failed to fetch user", error: error.message });
  }
});

// change Password
const changePassword = asyncHandler(async (req, res) => {
  try {
    const { oldPassword, newPassword } = req.body;
    const user = await User.findByPk(req.user.id);

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    const isMatch = await bcrypt.compare(oldPassword, user.password);
    if (!isMatch) {
      return res.status(401).json({ message: "Invalid Credentials" });
    }

    if (oldPassword === newPassword) {
      return res.status(400).json({ message: "New password cannot be the same as the old password" });
    }
    if (newPassword.length < 8) {
      return res.status(400).json({ message: "New password must be at least 8 characters" });
    }
    if (!newPassword.match(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/)) {
      return res.status(400).json({ message: "Password must include uppercase, lowercase, number, and special character" });
    }

    const hashedPassword = await bcrypt.hash(newPassword, 16);
    await user.update({ password: hashedPassword });

    res.status(200).json({ message: "Password changed successfully" });
  } catch (error) {
    res.status(500).json({ message: "Failed to change password", error: error.message });
  }
});

// forgot Password
const forgotPassword = asyncHandler(async (req, res) => {
  try {
    const { email } = req.body;
    const user = await User.findOne({ where: { email } });

    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }
    if (user.status !== "active") {
      return res.status(401).json({ message: "User is not active" });
    }
    if (!user.isVerified) {
      return res.status(401).json({ message: "User is not verified" });
    }
    if (user.role !== "user") {
      return res.status(401).json({ message: "User is not a user" });
    }
//  send mail on email 
    const resetToken = generateToken({ id: user.id });
    const resetLink = `${process.env.FRONTEND_URL}/reset-password/${resetToken}`;


    // Generate and send reset password token (not implemented)
    res.status(200).json({ message: "Reset password token sent" });
  } catch (error) {
    res.status(500).json({ message: "Failed to send reset password token", error: error.message });
  }
});


module.exports = {
  registerUser,
  login,
  logout,
  getAllUsersList,
  getInduvisualUserById,
  changePassword,
  forgotPassword,

};