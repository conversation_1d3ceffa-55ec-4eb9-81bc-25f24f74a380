const { DataTypes } = require("sequelize");
const { Sequelizer } = require("../configs/dbCon");

const CategoryMaster = Sequelizer.define(
  "CategoryMaster",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    categoryNameEng: {
      type: DataTypes.STRING(150),
      allowNull: false,
      unique: true,
    },
    categoryNameHin: {
      type: DataTypes.STRING(150),
      allowNull: true,
    },
    categoryCode: {
      type: DataTypes.STRING(50),
      allowNull: false,
      unique: true,
    },
    parentId: {
      type: DataTypes.UUID,
      allowNull: true, // NULL means it's a top-level category
      references: {
        model: "category_master",
        key: "id",
      },
    },
    isActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },
  },
  {
    tableName: "category_master",
    timestamps: true,
    paranoid: true, // enables soft delete
  }
);

// 🔹 Self-referencing associations
CategoryMaster.hasMany(CategoryMaster, {
  as: "subcategories",
  foreignKey: "parentId",
});

CategoryMaster.belongsTo(CategoryMaster, {
  as: "parent",
  foreignKey: "parentId",
});

module.exports = CategoryMaster;
