const { DataTypes } = require("sequelize");
const { Sequelizer } = require("../configs/dbCon");
const CategoryMaster = require("./categoryMasterModel");

const Product = Sequelizer.define(
  "Product",
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },

    productName: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },

    description: {
      type: DataTypes.TEXT,
      allowNull: true,
    },

    productPrice: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
    },

    discountPrice: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
    },

    stock: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
    },

    categoryMasterId: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: CategoryMaster,
        key: "id",
      },
    },

    brand: {
      type: DataTypes.STRING(100),
      allowNull: true,
    },

    images: {
      type: DataTypes.JSON, // Array of image URLs
      allowNull: true,
    },

    attributes: {
      type: DataTypes.JSON, // For colors, sizes, etc.
      allowNull: true,
    },

    isFeatured: {
      type: DataTypes.BOOLEAN,
      defaultValue: false,
    },

    isLive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true,
    },

    metaTitle: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },

    metaDescription: {
      type: DataTypes.STRING(500),
      allowNull: true,
    },
  },
  {
    tableName: "products",
    timestamps: true,
    paranoid: true, // enables soft delete
  }
);

// Association with CategoryMaster
Product.belongsTo(CategoryMaster, {
  foreignKey: "categoryMasterId",
  as: "categoryMaster",
});

module.exports = Product;
