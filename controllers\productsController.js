const asyncHandler = require("express-async-handler");
const CategoryMaster = require("../models/categoryMasterModel");
const Product = require("../models/productsModel");
const multer = require('multer');
const path = require("path");
const fs = require("fs");
const { Op } = require("sequelize");

// CREATE PRODUCT
const createProduct = asyncHandler(async (req, res) => {
  try {
    const {
      productName,
      description,
      productPrice,
      discountPrice,
      stock,
      categoryMasterId,
      brand,
      attributes,
      isFeatured,
      isLive,
      metaTitle,
      metaDescription,
    } = req.body;

    if (!productName || !productPrice || !categoryMasterId) {
      return res.status(400).json({
        message: "productName, productPrice and categoryMasterId are required",
      });
    }

    // ✅ Handle file upload(s)
    let imagePaths = [];
    if (req.files && req.files.length > 0) {
      const uploadFolder = path.join(__dirname, "../uploads/products");
      fs.mkdirSync(uploadFolder, { recursive: true });

      req.files.forEach((file) => {
        const finalFilePath = path.join(uploadFolder, file.filename);
        fs.renameSync(file.path, finalFilePath);
        imagePaths.push(`/uploads/products/${file.filename}`); // relative URL to save in DB
      });
    }

    // ✅ Save product in DB
    const product = await Product.create({
      productName,
      description,
      productPrice,
      discountPrice,
      stock,
      categoryMasterId,
      brand,
      images: imagePaths, // array of file paths
      attributes,
      isFeatured,
      isLive,
      metaTitle,
      metaDescription,
    });

    res
      .status(201)
      .json({ message: "Product created successfully", data: product });
  } catch (error) {
    console.error("Product creation error:", error.message);
    res
      .status(500)
      .json({ message: "Product creation failed", error: error.message });
  }
});

// GET ALL PRODUCTS
const getAllProducts = asyncHandler(async (req, res) => {
    try {
        const products = await Product.findAll({
            include: [
                {
                    model: CategoryMaster,
                    as: "categoryMaster",
                    attributes: ["id", "categoryNameEng", "categoryNameHin", "categoryCode"],
                },
            ],
            order: [["createdAt", "DESC"]],
        });

        res.status(200).json({ count: products.length, data: products });
    } catch (error) {
        res.status(500).json({ message: "Failed to fetch products", error: error.message });
    }
});

// GET SINGLE PRODUCT BY ID
const getProductById = asyncHandler(async (req, res) => {
    try {
        const { id } = req.params;

        const product = await Product.findByPk(id, {
            include: [
                {
                    model: CategoryMaster,
                    as: "categoryMaster",
                    attributes: ["id", "categoryNameEng", "categoryNameHin", "categoryCode"],
                },
            ],
        });

        if (!product) {
            return res.status(404).json({ message: "Product not found" });
        }

        res.status(200).json(product);
    } catch (error) {
        res.status(500).json({ message: "Failed to fetch product", error: error.message });
    }
});

// Get Product By Category 
const getProductByCategory = asyncHandler(async (req, res) => {
    try { 
        const { categoryMasterId } = req.params;

        const products = await Product.findAll({
            where: { categoryMasterId },
            include: [
                {
                    model: CategoryMaster,
                    as: "categoryMaster",
                    attributes: ["id", "categoryNameEng", "categoryNameHin", "categoryCode"],
                },
            ],
            order: [["createdAt", "DESC"]],
        });

        res.status(200).json({ count: products.length, data: products });
    } catch (error) {
        res.status(500).json({ message: "Failed to fetch products", error: error.message });
    }
});

// Category Master All Functions 
const createCategory = async (req, res) => {
  try {
    const { categoryNameEng, categoryNameHin, isActive, parentId } = req.body;

    // ✅ Only check for categoryNameEng
    if (!categoryNameEng) {
      return res.status(400).json({
        message: "categoryNameEng is required",
      });
    }

    // ✅ Generate base code (first 4 letters uppercase)
    let baseCode = categoryNameEng.trim().toUpperCase().substring(0, 4);

    // ✅ Find last used code with same prefix
    const existing = await CategoryMaster.findAll({
      where: { categoryCode: { [Op.like]: `${baseCode}-%` } },
      order: [["createdAt", "DESC"]],
    });

    let nextNumber = 1;
    if (existing.length > 0) {
      const lastCode = existing[0].categoryCode;
      const match = lastCode.match(/-(\d+)$/);
      if (match) {
        nextNumber = parseInt(match[1], 10) + 1;
      }
    }

    const categoryCode = `${baseCode}-${nextNumber}`;

    // ✅ Validate parent if provided
    if (parentId) {
      const parentCategory = await CategoryMaster.findByPk(parentId);
      if (!parentCategory) {
        return res.status(404).json({ message: "Parent category not found" });
      }
    }

    // ✅ Create record
    const category = await CategoryMaster.create({
      categoryNameEng,
      categoryNameHin,
      categoryCode,
      isActive,
      parentId: parentId || null,
    });

    return res.status(201).json({
      message: parentId
        ? "Subcategory created successfully"
        : "Category created successfully",
      data: category,
    });
  } catch (error) {
    console.error("Create Category Error:", error);
    return res.status(500).json({ message: error.message });
  }
};


// Get All Categories
const getCategories = async (req, res) => {
  try {
    const categories = await CategoryMaster.findAll({
      where: { parentId: null }, // ✅ only top-level categories
      include: [
        {
          model: CategoryMaster,
          as: "subcategories",
          include: [
            {
              model: CategoryMaster,
              as: "subcategories", // nested (sub-sub-categories)
            },
          ],
        },
      ],
      order: [["createdAt", "DESC"]],
    });

    return res.status(200).json({
      message: "Categories fetched successfully",
      data: categories,
    });
  } catch (error) {
    return res.status(500).json({ message: error.message });
  }
};


//update product 
const updateProduct = asyncHandler(async (req, res) => {
  try {
    const { id } = req.params;

    let product = await Product.findById(id);
    if (!product) {
      return res.status(404).json({ message: "Product not found" });
    }

    const {
      productName,
      description,
      productPrice,
      discountPrice,
      stock,
      categoryMasterId,
      brand,
      attributes,
      isFeatured,
      isLive,
      metaTitle,
      metaDescription,
    } = req.body;

    // ✅ Handle file upload(s)
    let imagePaths = product.images || []; // keep old images if no new ones
    if (req.files && req.files.length > 0) {
      const uploadFolder = path.join(__dirname, "../uploads/products");
      fs.mkdirSync(uploadFolder, { recursive: true });

      req.files.forEach((file) => {
        const finalFilePath = path.join(uploadFolder, file.filename);
        fs.renameSync(file.path, finalFilePath);
        imagePaths.push(`/uploads/products/${file.filename}`); // relative URL
      });
    }

    // ✅ Update product fields
    product.productName = productName || product.productName;
    product.description = description || product.description;
    product.productPrice = productPrice || product.productPrice;
    product.discountPrice = discountPrice || product.discountPrice;
    product.stock = stock || product.stock;
    product.categoryMasterId = categoryMasterId || product.categoryMasterId;
    product.brand = brand || product.brand;
    product.attributes = attributes || product.attributes;
    product.isFeatured = isFeatured !== undefined ? isFeatured : product.isFeatured;
    product.isLive = isLive !== undefined ? isLive : product.isLive;
    product.metaTitle = metaTitle || product.metaTitle;
    product.metaDescription = metaDescription || product.metaDescription;
    product.images = imagePaths;

    // ✅ Save updated product
    const updatedProduct = await product.save();

    res.json({
      message: "Product updated successfully",
      data: updatedProduct,
    });
  } catch (error) {
    console.error("Product update error:", error.message);
    res
      .status(500)
      .json({ message: "Product update failed", error: error.message });
  }
});




module.exports = {
    createProduct,
    getAllProducts,
    getProductById,
    getProductByCategory,
    createCategory,
    getCategories,
    updateProduct,
};
